<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.example</groupId>
    <artifactId>integration-hub</artifactId>
    <version>0.0.1</version>
    <name>integration-hub</name>
    <description>integration-hub</description>
    <url/>
    <licenses>
        <license/>
    </licenses>
    <developers>
        <developer/>
    </developers>
    <scm>
        <connection/>
        <developerConnection/>
        <tag/>
        <url/>
    </scm>
    <properties>
        <java.version>21</java.version>
        <json.version>20250107</json.version>
        <modelmapper.version>3.2.2</modelmapper.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.example</groupId>
            <artifactId>animal-sniffer-annotations</artifactId>
            <version>1.17</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/animal-sniffer-annotations-1.17.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>aopalliance</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/aopalliance-1.0.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>checker-qual</artifactId>
            <version>2.5.2</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/checker-qual-2.5.2.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.10</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/commons-codec-1.10.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.2</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/commons-logging-1.2.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>EdXML102</artifactId>
            <version>0.0.3</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/EdXML102-0.0.3.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>error_prone_annotations</artifactId>
            <version>2.2.0</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/error_prone_annotations-2.2.0.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>failureaccess</artifactId>
            <version>1.0.1</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/failureaccess-1.0.1.jar</systemPath>-->
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId> <!-- Hoặc 2.8.6 trở lên -->
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.example</groupId>-->
        <!--            <artifactId>guava</artifactId>-->
        <!--            <version>14.0.1</version>-->
        <!--            <scope>compile</scope>-->
        <!--            <systemPath>${project.basedir}/lib/guava-14.0.1.jar</systemPath>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>guice</artifactId>
            <version>4.2.0</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/guice-4.2.0.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.6</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/httpclient-4.5.6.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.10</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/httpcore-4.4.10.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.6</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/httpmime-4.5.6.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>j2objc-annotations</artifactId>
            <version>1.1</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/j2objc-annotations-1.1.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>javax-inject</artifactId>
            <version>1</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/javax.inject-1.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>jdom2</artifactId>
            <version>2.0.3</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/jdom2-2.0.3.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>json</artifactId>
            <version>20180813</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/json-20180813.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.11.3</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/jsoup-1.11.3.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>jsr305</artifactId>
            <version>3.0.2</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/jsr305-3.0.2.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>listenablefuture</artifactId>
            <version>9999.0-empty-to-avoid-conflict-with-guava</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>SDKVXP</artifactId>
            <version>2.0.0</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/SDKVXP-2.0.0.jar</systemPath>-->
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.example</groupId>-->
<!--            <artifactId>slf4j-api</artifactId>-->
<!--            <version>1.7.25</version>-->
<!--            <scope>compile</scope>-->
<!--            &lt;!&ndash;            <systemPath>${project.basedir}/lib/slf4j-api-1.7.25.jar</systemPath>&ndash;&gt;-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.12.0</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/xercesImpl-2.12.0.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>xml-apis</artifactId>
            <version>1.4.01</version>
            <scope>compile</scope>
            <!--            <systemPath>${project.basedir}/lib/xml-apis-1.4.01.jar</systemPath>-->
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.springframework.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- REDIS -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId><!-- Use the latest version available -->
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>${modelmapper.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.fds.flex.common</groupId>
            <artifactId>flex-common2</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>${json.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-maven-plugin</artifactId>
            <version>3.0.5</version>
        </dependency>

    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-maven-plugin</artifactId>
                <version>3.0.5</version>
            </plugin>
        </plugins>
    </build>

</project>
