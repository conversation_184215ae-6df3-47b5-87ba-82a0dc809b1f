package com.example.integration.controller;

import com.example.integration.config.PortalUtil;
import com.example.integration.config.PropKey;
import com.example.integration.constant.Constant;
import com.example.integration.dto.req.HangDoiGoiTinReqDTO;
import com.example.integration.dto.req.HangDoiGoiTinTrangThaiReqDTO;
import com.example.integration.dto.resp.BaseRespDTO;
import com.example.integration.dto.resp.HangDoiGoiTinRespDTO;
import com.example.integration.entity.HangDoiGoiTin;
import com.example.integration.entity.TepDuLieu;
import com.example.integration.entity.TrucTichHop;
import com.example.integration.entity.ext.HeThongKetNoiExt;
import com.example.integration.hub.action.HangDoiGoiTinAction;
import com.example.integration.hub.action.SDKVXPAction;
import com.example.integration.hub.action.TrucTichHopAction;
import com.example.integration.hub.action.impl.SDKVXPActionImpl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.ResponseUtil;
import com.fds.flex.common.ultility.Validator;
import com.fds.flex.common.ultility.string.StringPool;

import com.vpcp.services.model.RegisterAgencyResult;
import com.vpcp.services.request.AgencyRequest;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.ArrayList;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/integration/1.0/hangdoigoitin")
@Slf4j
@Validated
public class HangDoiGoiTinController {

	@Autowired
	HangDoiGoiTinAction action;

	@Autowired
	TrucTichHopAction trucTichHopAction;

	@PostMapping(value = "sendEdoc", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE}, produces = {
			MediaType.APPLICATION_JSON_VALUE})
	public ResponseEntity<?> sendEdoc(@RequestParam("File") MultipartFile file, @RequestHeader Map<String, String> headers) {
		JSONObject result = new JSONObject();

		if (file.isEmpty()) {
			return ResponseEntity.badRequest().body("File không được để trống");
		}

		if(!headers.containsKey("systemid") ) {
			result.put("ErrorCode", "-1");
			result.put("ErrorDesc", "That bai- No SystemId was found");
			return ResponseEntity.badRequest().body(result.toString());
		}

		String systemIdTrucSmall = headers.get("systemid");
		TrucTichHop trucTichHopCurrent = trucTichHopAction.findByMaMuc(systemIdTrucSmall);
		if (Validator.isNull(trucTichHopCurrent)) {
			result.put("ErrorCode", "-1");
			result.put("ErrorDesc", "That bai- No SystemId valid in trucTichHop");
			return ResponseEntity.badRequest().body(result.toString());
		}

		if(!headers.containsKey("messagetype")) {
			result.put("ErrorCode", "-1");
			result.put("ErrorDesc", "That bai- No messagetype was found");
			return ResponseEntity.badRequest().body(result.toString());
		}

		try {

			TepDuLieu fileSaved = action.uploadFile(file);

			if(Validator.isNull(fileSaved)) {
				throw new Exception("TepDuLieu is null");
			}

			String duongDanFile = fileSaved.getDuongDanURL();

			List<JsonNode> fromToNode = PortalUtil.getEdxmlFromTo(headers.get(Constant.HEADER_MESSAGE_TYPE), duongDanFile);
			if(Validator.isNull(fromToNode) || Validator.isNull(fromToNode.getFirst()) || Validator.isNull(fromToNode.get(1))) {

				result.put("ErrorCode", "-1");
				result.put("ErrorDesc", "That bai- EdXml format is error");
				return ResponseEntity.badRequest().body(result.toString());
			}

			HangDoiGoiTinReqDTO request = new HangDoiGoiTinReqDTO();
			HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi noiGuiGoiTin = new HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi();
			List<HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi> noiNhanGoiTins = new ArrayList<>();
			HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_TrucTichHop trucTichHop = new HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_TrucTichHop();
			HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_TepDuLieu noiDungGoiTin = new HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_TepDuLieu();

			JsonNode toNode   = fromToNode.get(1);
			HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi noiNhanGoiTin;

			if(toNode.isArray()) {
				for (JsonNode node : toNode) {
					noiNhanGoiTin = new HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi();
					noiNhanGoiTin.setMaKetNoi(node.get("OrganId").asText());
					noiNhanGoiTin.setTenKetNoi(node.get("OrganName").asText());
					noiNhanGoiTins.add(noiNhanGoiTin);
				}
			} else {
				noiNhanGoiTin = new HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi();
				JsonNode organIdNode = toNode.get("OrganId");
				if (organIdNode != null && !organIdNode.isNull()) {
					noiNhanGoiTin.setMaKetNoi(organIdNode.asText());
				}
				JsonNode organNameNode = toNode.get("OrganName");
				if (organNameNode != null && !organNameNode.isNull()) {
					noiNhanGoiTin.setTenKetNoi(organNameNode.asText());
				}
				noiNhanGoiTins.add(noiNhanGoiTin);
			}
			String fromCode = null;

			try {
				JsonNode fromNode   = fromToNode.getFirst();

				// Extract fromCode from fromNode

				JsonNode organIdNode = fromNode.get("OrganId");
				if (organIdNode != null && !organIdNode.isNull()) {
					fromCode = organIdNode.asText();
				}

				if(Validator.isNull(fromCode)) {
					result.put("ErrorCode", "-1");
					result.put("ErrorDesc", "That bai- EdXml format is error| no fromCode was found");
					return ResponseEntity.badRequest().body(result.toString());
				}

				boolean fromCodeExists = false;
				for (HeThongKetNoiExt heThongKetNoi : trucTichHopCurrent.getHeThongKetNoi()) {
					if (fromCode.equals(heThongKetNoi.getMaKetNoi())) {
						fromCodeExists = true;
						break;
					}
				}

				if (!fromCodeExists) {
					result.put("ErrorCode", "-1");
					result.put("ErrorDesc", "That bai- MaDinhDanh " + fromCode + " is not valid");
					return ResponseEntity.badRequest().body(result.toString());
				}
			} catch (Exception e) {
				log.error("Error when get parse noi nhan: ", e);

				result.put("ErrorCode", "-7");
				result.put("ErrorDesc", e.getMessage());
				return ResponseEntity.internalServerError().body(result.toString());
			}

			noiGuiGoiTin.setMaKetNoi(fromCode);
			noiDungGoiTin.setMaDinhDanh(fileSaved.getMaDinhDanh());

			request.setNoiGuiGoiTin(noiGuiGoiTin);
			request.setNoiNhanGoiTin(noiNhanGoiTins);
			request.setKieuLoaiGoiTin(Constant.SEND_EDOC);
			request.setTepDuLieu(noiDungGoiTin);
			request.setDinhDangGoiTin(headers.get(Constant.HEADER_MESSAGE_TYPE));
			request.setHeaders(headers);

			HangDoiGoiTin hangDoiGoiTin = action.add(request, trucTichHopCurrent);

			result.put("status", "OK");
			result.put("ErrorCode", "0");
			result.put("ErrorDesc", "Thanh cong");
			result.put("DocId", hangDoiGoiTin.getMaDinhDanh());

			return ResponseEntity.ok(result.toString());
		}catch (Exception ex) {
			log.error("File upload failed: " + ex.getMessage());

			result.put("ErrorCode", "-8");
			result.put("ErrorDesc", "Loi- " + ex.getMessage());
			return ResponseEntity.internalServerError().body(result.toString());
		}
	}

	@GetMapping(value = "/getEdoc",  produces = {
			MediaType.APPLICATION_JSON_VALUE })
	public ResponseEntity<?> getEdoc(@RequestHeader Map<String, String> headers) {
		JSONObject result = new JSONObject();
		if(!headers.containsKey("docid") ) {
			result.put("ErrorCode", "-1");
			result.put("ErrorDesc", "InvalidArgument");
			result.put("status", "FAIL");
			return ResponseEntity.badRequest().body(result.toString());
		}

		result.put("status", "OK");
		result.put("ErrorCode", "0");
		result.put("ErrorDesc", "Thanh cong");

		String docId = headers.get("docid");
		HangDoiGoiTin object = action.findByMaGoiTin(docId);

		if(Validator.isNull(object)) {
			result.put("ErrorCode", "-1");
			result.put("ErrorDesc", "Edoc not found");
			result.put("status", "FAIL");
			return ResponseEntity.badRequest().body(result.toString());
		}

		TepDuLieu noiDungGoiTin = object.getNoiDungGoiTin();

		if(Validator.isNull(noiDungGoiTin) || noiDungGoiTin.getDuongDanURL().isEmpty()) {
			result.put("ErrorCode", "-1");
			result.put("ErrorDesc", "File is null");
			result.put("status", "FAIL");
			return ResponseEntity.badRequest().body(result.toString());
		}

        try {
            String data = PortalUtil.encodeFileToBase64(noiDungGoiTin.getDuongDanURL());
			result.put("data", data);
			return ResponseEntity.ok().body(result.toString());
        } catch (IOException e) {
            log.error("Error when getEdoc with edocId: " + docId);
        }

		result.put("ErrorCode", "-1");
		result.put("ErrorDesc", "data edxml is not valid");
		result.put("status", "FAIL");
		return ResponseEntity.badRequest().body(result.toString());
	}


	@PostMapping(value = "updateStatus", produces = {
			MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
	public ResponseEntity<?> updateStatus(@RequestHeader Map<String, String> headers) {
		JSONObject result = new JSONObject();
		if(!headers.containsKey("docid") || !headers.containsKey("status") || !headers.containsKey("systemid")) {
			result.put("ErrorCode", "-1");
			result.put("ErrorDesc", "InvalidArgument");
			result.put("status", "FAIL");
			return ResponseEntity.badRequest().body(result.toString());
		}

		String docId = headers.get("docid");
		String status = headers.get("status");
		String noiNhanRequest = headers.get("systemid");

		HangDoiGoiTin hangDoiGoiTin = action.updateTrangThai(docId, status, noiNhanRequest);

		if(Validator.isNull(hangDoiGoiTin)) {
			result.put("ErrorCode", "-1");
			result.put("ErrorDesc", "No receiver");
			result.put("status", "FAIL");
			return ResponseEntity.badRequest().body(result.toString());
		}

		result.put("status", "OK");
		result.put("ErrorCode", "0");
		result.put("ErrorDesc", "Thanh cong");
		return ResponseEntity.ok().body(result.toString());
	}
	@PostMapping(value = "registerAgency", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> registerAgency(@RequestBody AgencyRequest request) {

		String secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
		String systemId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
		String hostLgsp = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_HOSTLGSP), StringPool.BLANK);


		SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);
		RegisterAgencyResult agencyResult = sdkVxpAction.registerAgency(request);


		JSONObject result = new JSONObject();
		result.put("status", agencyResult.getStatus());
		result.put("ErrorCode", agencyResult.getErrorCode());
		result.put("ErrorDesc", agencyResult.getErrorDesc());


		if (!"0".equals(agencyResult.getErrorCode())) {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result.toString());
		}
		return ResponseEntity.ok(result.toString());
	}

	@PostMapping(value = "getReceivedEdocList", produces = {
			MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
	public ResponseEntity<?> getReceivedEdocList(@RequestHeader Map<String, String> headers) {
		JSONObject result = new JSONObject();
		JSONArray data = new JSONArray();

		result.put("ErrorDesc", "Thanh cong");
		result.put("ErrorCode", "0");
		result.put("status", "OK");
		result.put("data", data);

		if(!headers.containsKey("systemid") ) {
			result.put("ErrorCode", "-1");
			result.put("ErrorDesc", "That bai- No SystemId was found");
			return ResponseEntity.badRequest().body(result.toString());
		}

		if(!headers.containsKey(Constant.HEADER_MESSAGE_TYPE)) {
			result.put("ErrorCode", "-1");
			result.put("ErrorDesc", "That bai- No messagetype was found");
			return ResponseEntity.badRequest().body(result.toString());
		}
		String trucNhan = headers.get("systemid");
		String messagetype = headers.get(Constant.HEADER_MESSAGE_TYPE);

		// Get list of allowed NoiNhan from TrucTichHop
		List<String> listNoiNhan = new ArrayList<>();
		if (Validator.isNotNull(trucNhan)) {
			TrucTichHop trucTichHop = trucTichHopAction.findByMaMuc(trucNhan);
			if (Validator.isNotNull(trucTichHop) && Validator.isNotNull(trucTichHop.getHeThongKetNoi())) {
				for (HeThongKetNoiExt heThongKetNoi : trucTichHop.getHeThongKetNoi()) {
					if (Validator.isNotNull(heThongKetNoi.getMaKetNoi())) {
						listNoiNhan.add(heThongKetNoi.getMaKetNoi());
					}
				}
			}
		}

		Page<HangDoiGoiTin> hangDoiGoiTinPage = action.filter(null, trucNhan,  null,
				null, null, messagetype, null,0, 50, null, null, listNoiNhan);

		if(Validator.isNull(hangDoiGoiTinPage) || hangDoiGoiTinPage.getContent().isEmpty()) {
			log.warn("Not found hangDoiGoiTin for systemId: " + trucNhan);
			return ResponseEntity.ok(result.toString());
		}

		List<HangDoiGoiTin> hangDoiGoiTins = hangDoiGoiTinPage.getContent();
		JSONObject oneData;
		boolean isInit;

		for(HangDoiGoiTin hangDoiGoiTin: hangDoiGoiTins) {
			isInit = true;
			List<HeThongKetNoiExt> noiNhanGoiTins = hangDoiGoiTin.getNoiNhanGoiTin();
			for(HeThongKetNoiExt noiNhanGoiTin : noiNhanGoiTins) {
				if(!noiNhanGoiTin.getTrangThaiLienThong().getMaMuc().equals(Constant.STATUS_INITIAL)) {
					isInit = false;
				}
				//chỉ lấy những gói tin đang ở trạng thái init, những gói tin đang được xử lý thì hiện ra làm con mẹ gì
				if(!isInit) {
					continue;
				}

				oneData = new JSONObject();
				oneData.put("serviceType", "eDoc");
				oneData.put("messagetype", hangDoiGoiTin.getDinhDangGoiTin());
				oneData.put("from", Validator.isNotNull(hangDoiGoiTin.getNoiGuiGoiTin()) ? hangDoiGoiTin.getNoiGuiGoiTin().getMaKetNoi() : "");
				oneData.put("to", noiNhanGoiTin.getMaKetNoi());
				oneData.put("status", noiNhanGoiTin.getTrangThaiLienThong().getMaMuc());
				oneData.put("docId", hangDoiGoiTin.getMaGoiTin());
				oneData.put("created_time", Validator.isNotNull(hangDoiGoiTin.getNoiGuiGoiTin()) ? hangDoiGoiTin.getNoiGuiGoiTin().getTimeVdxpCreate() : "");
				oneData.put("updated_time", Validator.isNotNull(hangDoiGoiTin.getNoiGuiGoiTin()) ? hangDoiGoiTin.getNoiGuiGoiTin().getTimeVdxpUpdate() : "");
				data.put(oneData);
			}
		}

		return ResponseEntity.ok(result.toString());
	}

//	@PostMapping(value = "", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE }, produces = {
//			MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
//	public ResponseEntity<?> add(@RequestBody HangDoiGoiTinReqDTO request, @RequestHeader Map<String, String> headers) {
//		headers.entrySet().removeIf(entry -> !entry.getKey().equalsIgnoreCase(Constant.X_LGSP_FROM)
//				&& !entry.getKey().equalsIgnoreCase(Constant.X_LGSP_TO)
//				&& !entry.getKey().equalsIgnoreCase(Constant.FROM));
//
//		request.setHeaders(headers);
//
//		HangDoiGoiTin object = action.add(request);
//
//		return ResponseEntity.ok(new HangDoiGoiTinRespDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
//				request, object));
//	}

	@PostMapping(value = "/upload/file", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {
			MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
	public ResponseEntity<?> uploadFile(@RequestParam MultipartFile file) {

		if (file.isEmpty()) {
			return ResponseEntity.badRequest().body("Please upload a file.");
		}

		try {
			TepDuLieu fileSaved = action.uploadFile(file);
			fileSaved.setDuongDanURL("----");
			return ResponseEntity.ok(fileSaved);
		} catch (Exception ex) {
			return ResponseEntity.internalServerError().body("File upload failed: " + ex.getMessage());
		}
	}

	@PostMapping(value = "/view/file/{id}")
	public ResponseEntity<?> viewFile(@PathVariable String id) {
		try {
			File file = action.viewFile(id);
			if (Validator.isNull(file)) {
				return ResponseEntity.badRequest().body("file is null");
			}

			FileSystemResource resource = new FileSystemResource(file);

			// Set headers to indicate a file download
			HttpHeaders headers = new HttpHeaders();
			headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getName());
			headers.add(HttpHeaders.CONTENT_TYPE, "application/octet-stream");  // You can set the proper MIME type here

			// Return the file with 200 OK status
			return ResponseEntity.ok()
					.headers(headers)
					.body(resource);
		} catch (Exception ex) {
			return ResponseEntity.internalServerError().body("View file failed: " + ex.getMessage());
		}
	}

	@PutMapping(value = "/{id}", consumes = { MediaType.APPLICATION_JSON_VALUE,
			MediaType.TEXT_PLAIN_VALUE }, produces = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
	public ResponseEntity<?> updateTrangThai(@PathVariable String id, @RequestBody HangDoiGoiTinTrangThaiReqDTO request) throws Exception {

		log.debug("Get body update HangDoiGoiTin: {} ", new JSONObject(request));

		HangDoiGoiTin object = action.update(id, request);

		return ResponseEntity.ok(object);

	}

	// @PreAuthorize("hasPermission('HangDoiGoiTin','UPDATE')")
	@PostMapping(value = "/{id}", consumes = { MediaType.APPLICATION_JSON_VALUE,
			MediaType.TEXT_PLAIN_VALUE }, produces = { MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE })
	public ResponseEntity<?> update(@PathVariable String id, @RequestBody HangDoiGoiTinReqDTO request) {

		log.debug("Get body update HangDoiGoiTin: {} ", new JSONObject(request));

		HangDoiGoiTin object = action.update(id, request);

		return ResponseEntity.ok(new HangDoiGoiTinRespDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
				request, object));

	}

	// @PreAuthorize("hasPermission('HangDoiGoiTin','READ')")
	@GetMapping(value = "/{id}")
	public ResponseEntity<?> findById(@PathVariable String id) {

		log.debug("Find HangDoiGoiTin: {} ", id);

		HangDoiGoiTin object = action.findById(id);

		return ResponseEntity.ok(
				new HangDoiGoiTinRespDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success", null, object));

	}

	// @PreAuthorize("hasPermission('HangDoiGoiTin','DELETE')")
	@DeleteMapping(value = "/{id}")
	public ResponseEntity<?> delete(@PathVariable String id) {

		log.debug("Delete HangDoiGoiTin: {} ", id);

		action.delete(id);

		return ResponseEntity.ok(new BaseRespDTO<String, String>(ResponseUtil.RespCode.SUCCESS.getCode(), "success",
				"success", id, "Delete success"));

	}

	// @PreAuthorize("hasPermission('HangDoiGoiTin','READ')")
	@GetMapping(value = "/filter")
	public ResponseEntity<?> filter(
			@RequestParam(required = false) String keyword,
			@RequestParam(required = false) String trucTichHop,
			@RequestParam(required = false) String trangThai,
			@RequestParam(required = false) String kieuLoaiGoiTin,
			@RequestParam(required = false) String noiGui,
			@RequestParam(required = false) String noiNhan,
			@RequestParam Integer page,
			@RequestParam Integer size,
			@RequestParam(required = false, defaultValue = "ThoiGianCapNhat") String orderFields,
			@RequestParam(required = false, defaultValue = "desc") String orderTypes) {

		log.debug(
				"Filter HangDoiGoiTin: trucTichHop: {}, noiGui: {}, noiNhan: {}, page:{}, size:{}, orderFields:{}, orderTypes:{}",
				trucTichHop, noiGui, noiNhan, page, size, orderFields, orderTypes);

		Page<HangDoiGoiTin> result = action.filter(keyword, trucTichHop, trangThai, noiGui, noiNhan,null, kieuLoaiGoiTin,  page, size, orderFields,
				orderTypes, null);

		return ResponseEntity.ok(result);
	}

	@GetMapping(value = "/export")
	public ResponseEntity<?> export(@RequestParam(required = true) String keyword,
			@RequestParam(required = true) String trucTichHop,
			@RequestParam(required = false) String trangThai,
									@RequestParam(required = false) String kieuLoaiGoiTin,
			@RequestParam(required = false) String noiGui,
			@RequestParam(required = false) String noiNhan, @RequestParam Integer page,
			@RequestParam Integer size,
			@RequestParam(required = false, defaultValue = "ThoiGianCapNhat") String orderFields,
			@RequestParam(required = false, defaultValue = "desc") String orderTypes) {

		log.debug(
				"Export HangDoiGoiTin: trucTichHop: {}, noiGui: {}, noiNhan: {}, page:{}, size:{}, orderFields:{}, orderTypes:{}",
				trucTichHop, noiGui, noiNhan, page, size, orderFields, orderTypes);

		Page<HangDoiGoiTin> result = action.filter(keyword, trucTichHop, trangThai, noiGui, noiNhan,null, kieuLoaiGoiTin,  page, size, orderFields,
				orderTypes, null);

		return ResponseEntity.ok(result);
	}
}
